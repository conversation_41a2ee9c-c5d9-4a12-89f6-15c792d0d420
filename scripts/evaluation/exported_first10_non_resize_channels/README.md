# 前10个非resize通道数据导出

## 文件说明

### 数据文件
- `channel_XXX_original.npy`: 通道XXX的原始数据 (形状: (16, 24))
- `channel_XXX_reconstructed.npy`: 通道XXX的重建数据 (形状: (16, 24))
- `channel_XXX_difference.npy`: 通道XXX的差异数据 (|重建 - 原始|)
- `channel_XXX_stats.npy`: 通道XXX的统计信息

### 汇总文件
- `summary_info.npy`: 汇总信息，包含通道列表、形状等基本信息

## 导出的通道
前10个非resize通道: [0, 6, 7, 9, 10, 11, 12, 14, 17, 19]

## 量化参数
- 量化步长: 0.00097752
- 1/2量化步长阈值: 0.00048876

## 数据范围
- 原始数据范围: [-13.448033, 14.595056]
- 重建数据范围: [-13.448033, 14.595056]

## 使用示例

```python
import numpy as np

# 加载通道0的数据
original = np.load('channel_000_original.npy')
reconstructed = np.load('channel_000_reconstructed.npy')
difference = np.load('channel_000_difference.npy')
stats = np.load('channel_000_stats.npy', allow_pickle=True).item()

print(f"通道0统计信息: {stats}")
```

## 分析目的
这些数据用于详细分析FCTM编解码器中量化/逆量化过程的精度损失问题。
通过对比原始数据和重建数据，可以定位具体的误差来源。
