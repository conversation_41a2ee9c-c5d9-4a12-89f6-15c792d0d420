#!/usr/bin/env python3
"""
比较归一化对相对误差的影响
分析: 原始数据vs重建数据 vs 归一化数据vs解包数据
"""

import numpy as np
import os
import sys

def calculate_relative_error(data1, data2):
    """计算相对误差"""
    # 避免除零错误
    denominator = np.abs(data1) + 1e-8
    relative_error = np.abs(data1 - data2) / denominator
    return relative_error

def load_all_data(base_path):
    """加载所有相关数据"""
    
    # 编码端数据路径
    encode_path = os.path.join(base_path, "exported_encode_data", "frame_0000")
    # 解码端数据路径  
    decode_path = os.path.join(base_path, "exported_decode_data", "frame_0000")
    
    print(f"加载数据路径: {encode_path}")
    
    # 加载编码端数据
    original_tensor = np.load(os.path.join(encode_path, "original_tensor.npy"))
    normalized_tensor = np.load(os.path.join(encode_path, "normalized_tensor.npy"))
    
    # 加载解码端数据
    unpacked_tensor = np.load(os.path.join(decode_path, "unpacked_tensor.npy"))
    reconstructed_tensor = np.load(os.path.join(decode_path, "reconstructed_tensor.npy"))
    
    # 加载resize通道信息
    resize_info = np.load(os.path.join(encode_path, "resize_channel_info.npy"), allow_pickle=True)
    
    print(f"原始数据形状: {original_tensor.shape}")
    print(f"归一化数据形状: {normalized_tensor.shape}")
    print(f"解包数据形状: {unpacked_tensor.shape}")
    print(f"重建数据形状: {reconstructed_tensor.shape}")
    
    return original_tensor, normalized_tensor, unpacked_tensor, reconstructed_tensor, resize_info

def analyze_data_ranges(original, normalized, unpacked, reconstructed, resize_info):
    """分析数据范围"""
    
    # 处理batch维度
    if len(unpacked.shape) == 4:
        unpacked = unpacked[0]
    if len(reconstructed.shape) == 4:
        reconstructed = reconstructed[0]
    
    # 获取resize通道信息
    resized_channels = set()
    if isinstance(resize_info, dict) and 'resize_channels' in resize_info:
        resized_channels = set(resize_info['resize_channels'])
    elif hasattr(resize_info, 'item') and isinstance(resize_info.item(), dict):
        resize_dict = resize_info.item()
        if 'resize_channels' in resize_dict:
            resized_channels = set(resize_dict['resize_channels'])
    
    print(f"\n数据范围分析:")
    print("="*60)
    
    # 分析几个代表性通道
    test_channels = [0, 1, 2, 3, 4]  # 前5个通道
    
    for ch in test_channels:
        is_resized = ch in resized_channels
        
        orig_ch = original[ch]
        norm_ch = normalized[ch]
        unpack_ch = unpacked[ch]
        recon_ch = reconstructed[ch]
        
        print(f"\n通道 {ch} ({'resized' if is_resized else 'non-resized'}):")
        print(f"  原始数据范围: [{orig_ch.min():.6f}, {orig_ch.max():.6f}], 均值: {orig_ch.mean():.6f}")
        print(f"  归一化数据范围: [{norm_ch.min():.6f}, {norm_ch.max():.6f}], 均值: {norm_ch.mean():.6f}")
        print(f"  解包数据范围: [{unpack_ch.min():.6f}, {unpack_ch.max():.6f}], 均值: {unpack_ch.mean():.6f}")
        print(f"  重建数据范围: [{recon_ch.min():.6f}, {recon_ch.max():.6f}], 均值: {recon_ch.mean():.6f}")
        
        # 计算相对误差
        error1 = calculate_relative_error(orig_ch, recon_ch)
        error2 = calculate_relative_error(norm_ch, unpack_ch)
        
        print(f"  原始vs重建相对误差: {error1.mean():.6f} ({error1.mean()*100:.4f}%)")
        print(f"  归一化vs解包相对误差: {error2.mean():.6f} ({error2.mean()*100:.4f}%)")
        
        # 分析归一化的影响
        if orig_ch.max() != orig_ch.min():  # 避免除零
            norm_factor = 1.0 / (orig_ch.max() - orig_ch.min())
            print(f"  归一化因子: {norm_factor:.6f}")
            print(f"  原始数据动态范围: {orig_ch.max() - orig_ch.min():.6f}")
            print(f"  归一化后动态范围: {norm_ch.max() - norm_ch.min():.6f}")

def compare_error_types(original, normalized, unpacked, reconstructed, resize_info):
    """比较不同类型的误差"""
    
    # 处理batch维度
    if len(unpacked.shape) == 4:
        unpacked = unpacked[0]
    if len(reconstructed.shape) == 4:
        reconstructed = reconstructed[0]
    
    # 获取resize通道信息
    resized_channels = set()
    if isinstance(resize_info, dict) and 'resize_channels' in resize_info:
        resized_channels = set(resize_info['resize_channels'])
    elif hasattr(resize_info, 'item') and isinstance(resize_info.item(), dict):
        resize_dict = resize_info.item()
        if 'resize_channels' in resize_dict:
            resized_channels = set(resize_dict['resize_channels'])
    
    num_channels = original.shape[0]
    
    # 分别统计两种误差
    orig_recon_errors_resized = []
    norm_unpack_errors_resized = []
    orig_recon_errors_non_resized = []
    norm_unpack_errors_non_resized = []
    
    for ch in range(num_channels):
        # 计算两种相对误差
        error1 = calculate_relative_error(original[ch], reconstructed[ch])
        error2 = calculate_relative_error(normalized[ch], unpacked[ch])
        
        mean_error1 = np.mean(error1)
        mean_error2 = np.mean(error2)
        
        if ch in resized_channels:
            orig_recon_errors_resized.append(mean_error1)
            norm_unpack_errors_resized.append(mean_error2)
        else:
            orig_recon_errors_non_resized.append(mean_error1)
            norm_unpack_errors_non_resized.append(mean_error2)
    
    print(f"\n误差类型对比:")
    print("="*60)
    
    # Resized通道统计
    if orig_recon_errors_resized:
        orig_mean = np.mean(orig_recon_errors_resized)
        norm_mean = np.mean(norm_unpack_errors_resized)
        print(f"Resized通道 ({len(orig_recon_errors_resized)}个):")
        print(f"  原始vs重建平均相对误差: {orig_mean:.6f} ({orig_mean*100:.4f}%)")
        print(f"  归一化vs解包平均相对误差: {norm_mean:.6f} ({norm_mean*100:.4f}%)")
        print(f"  误差比值 (归一化/原始): {norm_mean/orig_mean:.2f}x")
    
    # 非Resized通道统计
    if orig_recon_errors_non_resized:
        orig_mean = np.mean(orig_recon_errors_non_resized)
        norm_mean = np.mean(norm_unpack_errors_non_resized)
        print(f"\n非Resized通道 ({len(orig_recon_errors_non_resized)}个):")
        print(f"  原始vs重建平均相对误差: {orig_mean:.6f} ({orig_mean*100:.4f}%)")
        print(f"  归一化vs解包平均相对误差: {norm_mean:.6f} ({norm_mean*100:.4f}%)")
        print(f"  误差比值 (归一化/原始): {norm_mean/orig_mean:.2f}x")

def main():
    # 数据路径
    base_path = "fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data"
    
    if not os.path.exists(base_path):
        print(f"错误: 路径不存在 {base_path}")
        return
    
    try:
        # 加载所有数据
        original, normalized, unpacked, reconstructed, resize_info = load_all_data(base_path)
        
        # 分析数据范围
        analyze_data_ranges(original, normalized, unpacked, reconstructed, resize_info)
        
        # 比较误差类型
        compare_error_types(original, normalized, unpacked, reconstructed, resize_info)
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
