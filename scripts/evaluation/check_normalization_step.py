#!/usr/bin/env python3
"""
检查编码端原始特征到分组归一化(0-1)这一步是否有问题
"""

import numpy as np
import os
import sys

def check_normalization_process(base_path):
    """检查归一化过程"""
    
    # 编码端数据路径
    encode_path = os.path.join(base_path, "exported_encode_data", "frame_0000")
    
    print(f"检查路径: {encode_path}")
    
    # 加载数据
    original_tensor = np.load(os.path.join(encode_path, "original_tensor.npy"))
    normalized_tensor = np.load(os.path.join(encode_path, "normalized_tensor.npy"))
    
    # 尝试加载归一化参数
    try:
        normalization_params = np.load(os.path.join(encode_path, "normalization_params.npy"), allow_pickle=True)
        print(f"归一化参数文件存在")
    except:
        normalization_params = None
        print(f"归一化参数文件不存在或无法加载")
    
    print(f"原始数据形状: {original_tensor.shape}")
    print(f"归一化数据形状: {normalized_tensor.shape}")
    
    return original_tensor, normalized_tensor, normalization_params

def analyze_normalization_logic(original, normalized, params):
    """分析归一化逻辑"""
    
    print(f"\n归一化逻辑分析:")
    print("="*60)
    
    num_channels = original.shape[0]
    
    # 检查前几个通道的归一化
    test_channels = [0, 1, 2, 3, 4, 163]  # 包括最后一个通道
    
    for ch in test_channels:
        if ch >= num_channels:
            continue
            
        orig_ch = original[ch]
        norm_ch = normalized[ch]
        
        # 计算原始数据的统计信息
        orig_min = orig_ch.min()
        orig_max = orig_ch.max()
        orig_range = orig_max - orig_min
        
        # 计算归一化数据的统计信息
        norm_min = norm_ch.min()
        norm_max = norm_ch.max()
        norm_range = norm_max - norm_min
        
        print(f"\n通道 {ch}:")
        print(f"  原始数据: min={orig_min:.6f}, max={orig_max:.6f}, range={orig_range:.6f}")
        print(f"  归一化数据: min={norm_min:.6f}, max={norm_max:.6f}, range={norm_range:.6f}")
        
        # 检查归一化是否正确
        if orig_range > 1e-8:  # 避免除零
            # 标准min-max归一化: (x - min) / (max - min)
            expected_normalized = (orig_ch - orig_min) / orig_range
            
            # 检查是否匹配
            diff = np.abs(norm_ch - expected_normalized)
            max_diff = diff.max()
            mean_diff = diff.mean()
            
            print(f"  预期归一化范围: [0.000000, 1.000000]")
            print(f"  实际归一化范围: [{norm_min:.6f}, {norm_max:.6f}]")
            print(f"  与标准min-max归一化的差异: max={max_diff:.8f}, mean={mean_diff:.8f}")
            
            # 判断是否是标准min-max归一化
            if max_diff < 1e-6:
                print(f"  ✓ 符合标准min-max归一化")
            else:
                print(f"  ✗ 不符合标准min-max归一化")
                
                # 检查是否是其他归一化方式
                # 检查是否是带偏移的归一化
                if norm_min > 0.1 and norm_max < 0.9:
                    print(f"  可能使用了带偏移的归一化方式")
                
                # 检查几个像素点的具体计算
                print(f"  前5个像素点详细对比:")
                flat_orig = orig_ch.flatten()[:5]
                flat_norm = norm_ch.flatten()[:5]
                flat_expected = expected_normalized.flatten()[:5]
                
                for i in range(5):
                    print(f"    像素{i}: 原始={flat_orig[i]:.6f}, 实际归一化={flat_norm[i]:.6f}, 预期归一化={flat_expected[i]:.6f}")
        else:
            print(f"  ⚠ 原始数据范围太小，可能是常数通道")
            if np.allclose(norm_ch, norm_ch.flat[0]):
                print(f"  归一化后也是常数: {norm_ch.flat[0]:.6f}")

def check_normalization_params(params):
    """检查归一化参数"""
    
    if params is None:
        print(f"\n归一化参数: 无")
        return
    
    print(f"\n归一化参数分析:")
    print("="*60)
    
    if hasattr(params, 'item') and params.size == 1:
        try:
            params_item = params.item()
            if isinstance(params_item, dict):
                params_dict = params_item
                print(f"参数类型: 字典")
                print(f"参数键: {list(params_dict.keys())}")

                for key, value in params_dict.items():
                    if isinstance(value, np.ndarray):
                        print(f"  {key}: shape={value.shape}, dtype={value.dtype}")
                        if value.size <= 10:
                            print(f"    值: {value}")
                        else:
                            print(f"    前5个值: {value.flat[:5]}")
                    else:
                        print(f"  {key}: {value}")
            else:
                print(f"参数类型: {type(params_item)}")
                print(f"参数值: {params_item}")
        except:
            print(f"无法解析参数内容")
    else:
        print(f"参数类型: {type(params)}")
        print(f"参数形状: {params.shape if hasattr(params, 'shape') else 'N/A'}")
        if hasattr(params, 'shape') and params.size <= 20:
            print(f"参数值: {params}")

def check_range_validity(normalized):
    """检查归一化数据的范围有效性"""
    
    print(f"\n归一化数据范围检查:")
    print("="*60)
    
    global_min = normalized.min()
    global_max = normalized.max()
    
    print(f"全局范围: [{global_min:.6f}, {global_max:.6f}]")
    
    # 检查是否在[0,1]范围内
    if global_min >= 0.0 and global_max <= 1.0:
        print(f"✓ 数据在[0,1]范围内")
    else:
        print(f"✗ 数据超出[0,1]范围")
        
        if global_min < 0:
            print(f"  有负值: 最小值 = {global_min:.6f}")
        if global_max > 1:
            print(f"  超过1: 最大值 = {global_max:.6f}")
    
    # 检查每个通道的范围
    num_channels = normalized.shape[0]
    out_of_range_channels = []
    
    for ch in range(num_channels):
        ch_min = normalized[ch].min()
        ch_max = normalized[ch].max()
        
        if ch_min < -1e-6 or ch_max > 1.0 + 1e-6:  # 允许小的数值误差
            out_of_range_channels.append((ch, ch_min, ch_max))
    
    if out_of_range_channels:
        print(f"\n超出[0,1]范围的通道 ({len(out_of_range_channels)}个):")
        for ch, ch_min, ch_max in out_of_range_channels[:10]:  # 只显示前10个
            print(f"  通道 {ch}: [{ch_min:.6f}, {ch_max:.6f}]")
        if len(out_of_range_channels) > 10:
            print(f"  ... 还有 {len(out_of_range_channels)-10} 个通道")
    else:
        print(f"✓ 所有通道都在[0,1]范围内")

def main():
    # 数据路径
    base_path = "fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data"
    
    if not os.path.exists(base_path):
        print(f"错误: 路径不存在 {base_path}")
        return
    
    try:
        # 加载数据
        original, normalized, params = check_normalization_process(base_path)
        
        # 分析归一化逻辑
        analyze_normalization_logic(original, normalized, params)
        
        # 检查归一化参数
        check_normalization_params(params)
        
        # 检查范围有效性
        check_range_validity(normalized)
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
