#!/usr/bin/env python3
"""
分析第0帧编码端归一化数据与解码端解包后数据的相对误差
"""

import numpy as np
import os
import sys

def load_data(base_path):
    """加载编码端和解码端数据"""
    
    # 编码端数据路径
    encode_path = os.path.join(base_path, "exported_encode_data", "frame_0000")
    # 解码端数据路径  
    decode_path = os.path.join(base_path, "exported_decode_data", "frame_0000")
    
    print(f"编码端路径: {encode_path}")
    print(f"解码端路径: {decode_path}")
    
    # 加载编码端归一化数据
    encode_normalized = np.load(os.path.join(encode_path, "normalized_tensor.npy"))
    print(f"编码端归一化数据形状: {encode_normalized.shape}")
    
    # 加载解码端解包后数据
    decode_unpacked = np.load(os.path.join(decode_path, "unpacked_tensor.npy"))
    print(f"解码端解包数据形状: {decode_unpacked.shape}")
    
    # 加载resize通道信息
    resize_info = np.load(os.path.join(encode_path, "resize_channel_info.npy"), allow_pickle=True)
    print(f"Resize通道信息形状: {resize_info.shape}")
    print(f"Resize通道信息: {resize_info}")
    
    return encode_normalized, decode_unpacked, resize_info

def calculate_relative_error(data1, data2):
    """计算相对误差"""
    # 避免除零错误
    denominator = np.abs(data1) + 1e-8
    relative_error = np.abs(data1 - data2) / denominator
    return relative_error

def analyze_channels(encode_data, decode_data, resize_info):
    """分析各通道的相对误差"""

    # 如果有batch维度，去掉
    if len(decode_data.shape) == 4:
        decode_data = decode_data[0]

    # 确保数据形状一致
    if encode_data.shape != decode_data.shape:
        print(f"警告: 数据形状不一致!")
        print(f"编码端: {encode_data.shape}")
        print(f"解码端: {decode_data.shape}")
        return None, None

    num_channels = encode_data.shape[0]
    print(f"总通道数: {num_channels}")

    # 确定哪些通道是resized的
    resized_channels = set()
    if isinstance(resize_info, dict) and 'resize_channels' in resize_info:
        resized_channels = set(resize_info['resize_channels'])
    elif hasattr(resize_info, 'item') and isinstance(resize_info.item(), dict):
        resize_dict = resize_info.item()
        if 'resize_channels' in resize_dict:
            resized_channels = set(resize_dict['resize_channels'])

    print(f"Resized通道数量: {len(resized_channels)}")
    print(f"Resized通道: {sorted(list(resized_channels))[:10]}..." if len(resized_channels) > 10 else f"Resized通道: {sorted(list(resized_channels))}")
    
    # 分别统计resized和非resized通道
    resized_errors = []
    non_resized_errors = []
    
    for ch in range(num_channels):
        # 计算该通道的相对误差
        ch_encode = encode_data[ch]
        ch_decode = decode_data[ch]
        
        rel_error = calculate_relative_error(ch_encode, ch_decode)
        mean_error = np.mean(rel_error)
        
        if ch in resized_channels:
            resized_errors.append(mean_error)
        else:
            non_resized_errors.append(mean_error)
        
        # 打印前几个和后几个通道的详细信息
        if ch < 5 or ch >= num_channels - 5:
            print(f"通道 {ch} ({'resized' if ch in resized_channels else 'non-resized'}): "
                  f"平均相对误差 = {mean_error:.6f}")
    
    # 统计结果
    print("\n" + "="*60)
    print("统计结果:")
    print("="*60)
    
    if resized_errors:
        resized_mean = np.mean(resized_errors)
        resized_std = np.std(resized_errors)
        print(f"Resized通道 ({len(resized_errors)}个):")
        print(f"  平均相对误差: {resized_mean:.6f} ± {resized_std:.6f}")
        print(f"  最小误差: {np.min(resized_errors):.6f}")
        print(f"  最大误差: {np.max(resized_errors):.6f}")
    else:
        print("没有找到resized通道")
    
    if non_resized_errors:
        non_resized_mean = np.mean(non_resized_errors)
        non_resized_std = np.std(non_resized_errors)
        print(f"\n非Resized通道 ({len(non_resized_errors)}个):")
        print(f"  平均相对误差: {non_resized_mean:.6f} ± {non_resized_std:.6f}")
        print(f"  最小误差: {np.min(non_resized_errors):.6f}")
        print(f"  最大误差: {np.max(non_resized_errors):.6f}")
    else:
        print("没有找到非resized通道")
    
    return resized_errors, non_resized_errors

def main():
    # 数据路径
    base_path = "fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data"
    
    if not os.path.exists(base_path):
        print(f"错误: 路径不存在 {base_path}")
        return
    
    try:
        # 加载数据
        encode_data, decode_data, resize_info = load_data(base_path)
        
        # 分析通道误差
        resized_errors, non_resized_errors = analyze_channels(encode_data, decode_data, resize_info)
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
