#!/usr/bin/env python3
"""
详细检查归一化参数
"""

import numpy as np
import os

def main():
    # 数据路径
    encode_path = "fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_encode_data/frame_0000"
    
    # 加载数据
    original_tensor = np.load(os.path.join(encode_path, "original_tensor.npy"))
    normalized_tensor = np.load(os.path.join(encode_path, "normalized_tensor.npy"))
    normalization_params = np.load(os.path.join(encode_path, "normalization_params.npy"), allow_pickle=True)
    
    print(f"归一化参数形状: {normalization_params.shape}")
    print(f"归一化参数类型: {normalization_params.dtype}")
    print(f"前10个通道的归一化参数:")
    print(normalization_params[:10])
    
    print(f"\n分析归一化公式:")
    print("="*60)
    
    # 分析前几个通道
    for ch in range(5):
        orig_ch = original_tensor[ch]
        norm_ch = normalized_tensor[ch]
        params = normalization_params[ch]
        
        print(f"\n通道 {ch}:")
        print(f"  原始数据范围: [{orig_ch.min():.6f}, {orig_ch.max():.6f}]")
        print(f"  归一化数据范围: [{norm_ch.min():.6f}, {norm_ch.max():.6f}]")
        print(f"  归一化参数: {params}")
        
        # 尝试不同的归一化公式
        min_val, max_val = params[0], params[1]
        
        # 公式1: 标准min-max归一化
        formula1 = (orig_ch - min_val) / (max_val - min_val)
        diff1 = np.abs(norm_ch - formula1).max()
        
        # 公式2: 分组归一化 (可能是全局min-max)
        global_min = normalization_params[:, 0].min()
        global_max = normalization_params[:, 1].max()
        formula2 = (orig_ch - global_min) / (global_max - global_min)
        diff2 = np.abs(norm_ch - formula2).max()
        
        # 公式3: 带偏移的归一化
        # 可能是 (x - min) / (max - min) * scale + offset
        range_val = max_val - min_val
        if range_val > 1e-8:
            # 尝试找到scale和offset
            norm_range = norm_ch.max() - norm_ch.min()
            scale = norm_range / range_val if range_val > 0 else 1
            offset = norm_ch.min() - (orig_ch.min() - min_val) / range_val * norm_range
            
            formula3 = (orig_ch - min_val) / range_val * norm_range + norm_ch.min()
            diff3 = np.abs(norm_ch - formula3).max()
        else:
            diff3 = float('inf')
        
        print(f"  标准min-max归一化误差: {diff1:.8f}")
        print(f"  全局min-max归一化误差: {diff2:.8f}")
        print(f"  带偏移归一化误差: {diff3:.8f}")
        
        # 找到最佳匹配
        if diff1 < 1e-6:
            print(f"  ✓ 使用标准min-max归一化: (x - {min_val:.6f}) / {max_val - min_val:.6f}")
        elif diff2 < 1e-6:
            print(f"  ✓ 使用全局min-max归一化: (x - {global_min:.6f}) / {global_max - global_min:.6f}")
        elif diff3 < 1e-6:
            print(f"  ✓ 使用带偏移的归一化")
        else:
            print(f"  ✗ 未找到匹配的归一化公式")
            
            # 手动分析几个点
            print(f"  手动分析前3个像素点:")
            for i in range(3):
                orig_val = orig_ch.flat[i]
                norm_val = norm_ch.flat[i]
                expected = (orig_val - min_val) / (max_val - min_val)
                print(f"    像素{i}: 原始={orig_val:.6f}, 归一化={norm_val:.6f}, 预期={expected:.6f}")

if __name__ == "__main__":
    main()
